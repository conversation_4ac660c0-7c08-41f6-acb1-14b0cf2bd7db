'use server';

import Http from '@/lib/http';

export interface CreateServiceRequest {
  name: string;
  port: string;
  target_port: string;
  type: string;
  cluster_ip?: string;
  external_ip?: string;
  namespace_id: number;
  deployment_id: number;
}

export interface UpdateServiceRequest {
  name: string;
  port: string;
  target_port: string;
  type: string;
  cluster_ip?: string;
  external_ip?: string;
  namespace_id: number;
  deployment_id: number;
}

export interface PartialUpdateServiceRequest {
  port: string;
  target_port: string;
  type: string;
}

export async function createService(data: CreateServiceRequest) {
  try {
    const response = await Http.post('/services', data);
    return response.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
}

export async function updateService(
  id: number,
  data: PartialUpdateServiceRequest
) {
  try {
    // First, fetch the existing service data
    const existingService: any = await getService(id);
    if (!existingService?.status) {
      throw new Error('Failed to fetch existing service data');
    }

    // Merge existing service data with the provided fields
    const mergedData: UpdateServiceRequest = {
      name: existingService.data.name,
      port: data.port,
      target_port: data.target_port,
      type: data.type,
      cluster_ip: existingService.data.cluster_ip,
      external_ip: existingService.data.external_ip,
      namespace_id: existingService.data.namespace_id,
      deployment_id: existingService.data.deployment_id,
    };

    const response = await Http.put(`/services/${id}`, mergedData);
    return response.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
}

export async function deleteService(id: number) {
  try {
    const response = await Http.delete(`/services/${id}`);
    return response.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
}

export async function getService(id: number) {
  try {
    const response = await Http.get(`/services/${id}`);
    return response.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
}

export async function getServices(filters?: {
  namespace_id?: number;
  deployment_id?: number;
  name?: string;
  type?: string;
}) {
  try {
    const searchParams = new URLSearchParams();

    if (filters?.namespace_id) {
      searchParams.append('namespace_id', filters.namespace_id.toString());
    }
    if (filters?.deployment_id) {
      searchParams.append('deployment_id', filters.deployment_id.toString());
    }
    if (filters?.name) {
      searchParams.append('name', filters.name);
    }
    if (filters?.type) {
      searchParams.append('type', filters.type);
    }

    const url = `/services${searchParams.toString() ? `?${searchParams.toString()}` : ''}`;
    const response = await Http.get(url);
    return response.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
}
