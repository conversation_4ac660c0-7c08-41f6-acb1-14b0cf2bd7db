'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2 } from 'lucide-react';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';

import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useServiceStore } from '@/store/service/action';
import { ServiceType } from '@/store/service/type';

const editServiceSchema = z.object({
  port: z.string().min(1, 'Port is required'),
  target_port: z.string().min(1, 'Target port is required'),
  type: z.enum(['ClusterIP', 'NodePort', 'LoadBalancer'], {
    required_error: 'Service type is required',
  }),
});

type EditServiceForm = z.infer<typeof editServiceSchema>;

interface EditServiceModalProps {
  service: ServiceType;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}

export function EditServiceModal({
  service,
  open,
  onOpenChange,
  onSuccess,
}: EditServiceModalProps) {
  const { updateService, updating } = useServiceStore();

  const form = useForm<EditServiceForm>({
    resolver: zodResolver(editServiceSchema),
    defaultValues: {
      port: service.port,
      target_port: service.target_port,
      type: service.type as 'ClusterIP' | 'NodePort' | 'LoadBalancer',
    },
  });

  useEffect(() => {
    if (open) {
      form.reset({
        port: service.port,
        target_port: service.target_port,
        type: service.type as 'ClusterIP' | 'NodePort' | 'LoadBalancer',
      });
    }
  }, [open, service, form]);

  const onSubmit = async (data: EditServiceForm) => {
    try {
      const response = await updateService(service.id, data);

      if (response?.status) {
        toast.success('Service updated successfully');
        onOpenChange(false);
        onSuccess?.();
      } else {
        toast.error('Failed to update service');
      }
    } catch (error) {
      console.error('Error updating service:', error);
      toast.error('Failed to update service');
    }
  };

  const handleClose = () => {
    form.reset();
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='sm:max-w-[425px]'>
        <DialogHeader>
          <DialogTitle>Edit Service</DialogTitle>
          <DialogDescription>
            Update the service configuration. Only port, target port, and type
            can be modified.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-4'>
            <div className='space-y-4'>
              {/* Service Name - Read Only */}
              <div className='space-y-2'>
                <label className='text-sm font-medium text-muted-foreground'>
                  Service Name (Read Only)
                </label>
                <div className='px-3 py-2 bg-muted rounded-md text-sm'>
                  {service.name}
                </div>
              </div>

              {/* Port */}
              <FormField
                control={form.control}
                name='port'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Port</FormLabel>
                    <FormControl>
                      <Input
                        placeholder='e.g., 80'
                        {...field}
                        disabled={updating}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Target Port */}
              <FormField
                control={form.control}
                name='target_port'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Target Port</FormLabel>
                    <FormControl>
                      <Input
                        placeholder='e.g., 8080'
                        {...field}
                        disabled={updating}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Service Type */}
              <FormField
                control={form.control}
                name='type'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Service Type</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      disabled={updating}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder='Select service type' />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value='ClusterIP'>ClusterIP</SelectItem>
                        <SelectItem value='NodePort'>NodePort</SelectItem>
                        <SelectItem value='LoadBalancer'>
                          LoadBalancer
                        </SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Read-only fields display */}
              <div className='space-y-2 pt-2 border-t'>
                <label className='text-sm font-medium text-muted-foreground'>
                  Other Properties (Read Only)
                </label>
                <div className='grid grid-cols-2 gap-2 text-xs'>
                  {service.cluster_ip && (
                    <div>
                      <span className='text-muted-foreground'>Cluster IP:</span>
                      <div className='font-mono'>{service.cluster_ip}</div>
                    </div>
                  )}
                  {service.external_ip && (
                    <div>
                      <span className='text-muted-foreground'>
                        External IP:
                      </span>
                      <div className='font-mono'>{service.external_ip}</div>
                    </div>
                  )}
                </div>
              </div>
            </div>

            <DialogFooter>
              <Button
                type='button'
                variant='outline'
                onClick={handleClose}
                disabled={updating}
              >
                Cancel
              </Button>
              <Button type='submit' disabled={updating}>
                {updating ? (
                  <>
                    <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                    Updating...
                  </>
                ) : (
                  'Update Service'
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
