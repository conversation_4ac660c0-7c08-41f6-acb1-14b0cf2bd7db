export const calculateProjectStatus = (project: any) => {
  if (!project) {
    return 'unknown';
  }

  const allComponents = [
    ...(project.deployments || []),
    ...(project.services || []),
    ...(project.ingress || []),
  ];

  if (allComponents.length === 0) {
    return 'unpublished';
  }

  const statusCounts = allComponents.reduce(
    (acc, component) => {
      const statusId = component.status?.id;
      acc[statusId] = (acc[statusId] || 0) + 1;
      return acc;
    },
    {} as Record<number, number>
  );

  const totalComponents = allComponents.length;

  // Priority order: error > destroyed > deleting > maintenance > updating > creating > unpublished > active

  // Check for error (id = 8)
  if (statusCounts[8] && statusCounts[8] >= 1) {
    return 'error';
  }

  // Check for destroyed (id = 9)
  if (statusCounts[9] && statusCounts[9] >= 1) {
    return 'destroyed';
  }

  // Check for deleting (id = 7)
  if (statusCounts[7] && statusCounts[7] >= 1) {
    return 'deleting';
  }

  // Check for maintenance (id = 6)
  if (statusCounts[6] && statusCounts[6] >= 1) {
    return 'maintenance';
  }

  // Check for updating (id = 5)
  if (statusCounts[5] && statusCounts[5] >= 1) {
    return 'updating';
  }

  // Check for creating (id = 2)
  if (statusCounts[2] && statusCounts[2] >= 1) {
    return 'creating';
  }

  // Check for unpublished (id = 1)
  if (statusCounts[1] && statusCounts[1] >= 1) {
    return 'unpublished';
  }

  // Check if all are active/published (id = 3)
  if (statusCounts[3] === totalComponents) {
    return 'active';
  }

  // Default fallback
  return 'mixed';
};
